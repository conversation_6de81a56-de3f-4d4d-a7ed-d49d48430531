#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试路径修复是否有效
"""

import sys
from pathlib import Path

def get_fixmorph_path():
    """自动检测FixMorph项目根目录"""
    current_path = Path(__file__).resolve()
    for parent in [current_path] + list(current_path.parents):
        if (parent / "FixMorph.py").exists():
            return str(parent)
    return str(Path.cwd())

def test_path_detection():
    """测试路径检测"""
    fixmorph_path = get_fixmorph_path()
    print("检测到的FixMorph路径:", fixmorph_path)
    
    # 验证路径是否正确
    fixmorph_py = Path(fixmorph_path) / "FixMorph.py"
    if fixmorph_py.exists():
        print("✅ 路径检测成功: FixMorph.py 存在")
    else:
        print("❌ 路径检测失败: FixMorph.py 不存在")
    
    # 测试输出目录
    output_dir = Path(fixmorph_path) / "output"
    print("输出目录:", output_dir)
    
    return fixmorph_path

if __name__ == "__main__":
    test_path_detection()
