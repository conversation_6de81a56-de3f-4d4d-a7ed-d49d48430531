#!/bin/bash

# FixMorph 大规模实验启动脚本
# 运行100个CVE实验，timeout=3600秒

echo "🚀 启动FixMorph大规模实验"
echo "=================================="
echo "实验数量: 100个CVE"
echo "超时时间: 3600秒"
echo "并发数: 8个worker"
echo "模式: Stage2 + 完整构建验证"
echo "=================================="

# 创建日志目录
mkdir -p logs/large_scale_experiment

# 获取当前时间戳
TIMESTAMP=$(date +%s)
LOG_FILE="logs/large_scale_experiment/experiment_${TIMESTAMP}.log"

echo "📝 日志文件: $LOG_FILE"
echo "🔄 启动后台实验..."

# 启动后台实验 - 完整构建验证模式
nohup python3.7 src_enhanced/experiment_runner.py \
  --data data/enhanced_data/enhanced_and_nvd_dataset.json \
  --output experiments/enhanced_dataset \
  --workers 8 \
  --timeout 3600 \
  --stage2 \
  --build-verification \
  --end 100 \
  --github-token "$(cat ~/.github_token 2>/dev/null || echo '')" \
  --keep-results \
  > "$LOG_FILE" 2>&1 &

# 获取进程ID
PID=$!
echo "✅ 实验已启动!"
echo "📊 进程ID: $PID"
echo "📝 日志文件: $LOG_FILE"

# 保存PID到文件
echo $PID > logs/large_scale_experiment/experiment_${TIMESTAMP}.pid

echo ""
echo "🔍 监控命令:"
echo "  查看实时日志: tail -f $LOG_FILE"
echo "  查看进程状态: ps aux | grep $PID"
echo "  停止实验: kill $PID"
echo ""
echo "📂 结果将保存到: experiments/enhanced_dataset/results/"
echo "📊 实验状态: experiments/enhanced_dataset/checkpoints/"

# 等待几秒钟确保进程启动
sleep 3

# 检查进程是否还在运行
if ps -p $PID > /dev/null; then
    echo "✅ 实验正在后台运行中..."
    echo "🎯 预计完成时间: $(date -d '+6 hours')"
else
    echo "❌ 实验启动失败，请检查日志: $LOG_FILE"
    exit 1
fi

echo ""
echo "🎉 大规模实验已成功启动到后台!"
