#!/usr/bin/env python3.7
# -*- coding: utf-8 -*-

"""
测试路径修复逻辑是否正确
"""

from pathlib import Path

def test_experiment_runner_logic():
    """测试ExperimentRunner的路径逻辑"""
    print("=== 测试 ExperimentRunner 路径逻辑 ===")
    
    # 模拟ExperimentRunner的路径检测逻辑
    fixmorph_path = None
    
    if fixmorph_path is None:
        # 从当前文件位置向上查找项目根目录
        current_path = Path(__file__).resolve()
        for parent in [current_path] + list(current_path.parents):
            if (parent / "FixMorph.py").exists():
                fixmorph_path = parent
                break
        if fixmorph_path is None:
            fixmorph_path = Path.cwd()
    
    fixmorph_path = Path(fixmorph_path)
    
    print(f"检测到的fixmorph_path: {fixmorph_path}")
    print(f"FixMorph.py存在: {(fixmorph_path / 'FixMorph.py').exists()}")
    
    return fixmorph_path

def test_performance_demo_logic():
    """测试PerformanceDemo的路径逻辑"""
    print("\n=== 测试 PerformanceDemo 路径逻辑 ===")
    
    # 模拟PerformanceDemo的路径检测逻辑
    output_dir = None
    
    if output_dir is None:
        # 从当前文件位置向上查找项目根目录
        current_path = Path(__file__).resolve()
        for parent in [current_path] + list(current_path.parents):
            if (parent / "FixMorph.py").exists():
                output_dir = parent / "output" / "performance_demo"
                break
        if output_dir is None:
            output_dir = Path.cwd() / "output" / "performance_demo"
    
    output_dir = Path(output_dir)
    
    print(f"检测到的output_dir: {output_dir}")
    print(f"父目录存在: {output_dir.parent.exists()}")
    
    return output_dir

def test_driver_logic():
    """测试driver.py的路径逻辑"""
    print("\n=== 测试 driver.py 路径逻辑 ===")
    
    # 模拟driver.py的路径检测逻辑
    def get_fixmorph_path():
        current_path = Path(__file__).resolve()
        for parent in [current_path] + list(current_path.parents):
            if (parent / "FixMorph.py").exists():
                return str(parent)
        return str(Path.cwd())
    
    tool_path = get_fixmorph_path()
    
    print(f"检测到的CONF_TOOL_PATH: {tool_path}")
    print(f"FixMorph.py存在: {Path(tool_path, 'FixMorph.py').exists()}")
    
    # 测试相关路径
    log_path = tool_path + "/logs/linux-1"
    output_path = tool_path + "/output/linux-1"
    
    print(f"日志路径: {log_path}")
    print(f"输出路径: {output_path}")
    
    return tool_path

def main():
    """主测试函数"""
    print("🔧 测试路径修复逻辑")
    print("=" * 50)
    
    try:
        fixmorph_path = test_experiment_runner_logic()
        output_dir = test_performance_demo_logic()
        tool_path = test_driver_logic()
        
        print("\n" + "=" * 50)
        print("🎉 所有路径逻辑测试通过!")
        print(f"项目根目录: {fixmorph_path}")
        print(f"输出目录: {output_dir}")
        print(f"工具路径: {tool_path}")
        
        # 验证路径一致性
        if str(fixmorph_path) == tool_path:
            print("✅ 路径检测一致性验证通过")
        else:
            print("❌ 路径检测不一致")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
