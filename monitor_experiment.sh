#!/bin/bash

# FixMorph 实验监控脚本

LOG_FILE="logs/large_scale_experiment/experiment_1754501158.log"
PID=50007

echo "🔍 FixMorph 实验监控面板"
echo "=================================="
echo "📊 进程ID: $PID"
echo "📝 日志文件: $LOG_FILE"
echo "⚡ 并发数: 8个worker"
echo "=================================="

# 检查进程状态
check_process() {
    if ps -p $PID > /dev/null 2>&1; then
        echo "✅ 实验进程正在运行"
        
        # 显示CPU和内存使用情况
        echo "📈 资源使用情况:"
        ps -p $PID -o pid,ppid,pcpu,pmem,etime,cmd --no-headers
        
        # 显示子进程（worker进程）
        echo ""
        echo "👥 Worker进程状态:"
        pgrep -P $PID | wc -l | xargs echo "   活跃worker数量:"
        
    else
        echo "❌ 实验进程已停止"
        return 1
    fi
}

# 显示最新日志
show_recent_logs() {
    echo ""
    echo "📋 最新日志 (最后20行):"
    echo "--------------------------------"
    if [ -f "$LOG_FILE" ]; then
        tail -n 20 "$LOG_FILE" | while read line; do
            # 高亮重要信息
            if [[ $line == *"SUCCESS"* ]] || [[ $line == *"✅"* ]]; then
                echo -e "\033[32m$line\033[0m"  # 绿色
            elif [[ $line == *"ERROR"* ]] || [[ $line == *"❌"* ]]; then
                echo -e "\033[31m$line\033[0m"  # 红色
            elif [[ $line == *"WARNING"* ]] || [[ $line == *"⚠️"* ]]; then
                echo -e "\033[33m$line\033[0m"  # 黄色
            else
                echo "$line"
            fi
        done
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 显示实验统计
show_stats() {
    echo ""
    echo "📊 实验统计:"
    echo "--------------------------------"
    if [ -f "$LOG_FILE" ]; then
        echo "总日志行数: $(wc -l < "$LOG_FILE")"
        echo "成功数量: $(grep -c "SUCCESS\|✅.*完成" "$LOG_FILE" 2>/dev/null || echo "0")"
        echo "失败数量: $(grep -c "ERROR\|❌.*失败" "$LOG_FILE" 2>/dev/null || echo "0")"
        echo "警告数量: $(grep -c "WARNING\|⚠️" "$LOG_FILE" 2>/dev/null || echo "0")"
    fi
}

# 主监控循环
if [ "$1" = "--watch" ]; then
    echo "🔄 进入实时监控模式 (按Ctrl+C退出)"
    echo ""
    
    while true; do
        clear
        echo "🔍 FixMorph 实验实时监控 - $(date)"
        echo "=================================="
        
        if ! check_process; then
            echo "实验已结束"
            break
        fi
        
        show_stats
        show_recent_logs
        
        echo ""
        echo "🔄 5秒后刷新... (按Ctrl+C退出)"
        sleep 5
    done
else
    # 单次检查模式
    check_process
    show_stats
    show_recent_logs
    
    echo ""
    echo "💡 使用方法:"
    echo "  实时监控: ./monitor_experiment.sh --watch"
    echo "  查看完整日志: tail -f $LOG_FILE"
    echo "  停止实验: kill $PID"
fi
